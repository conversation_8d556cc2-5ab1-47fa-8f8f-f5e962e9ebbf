{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0122918a", "metadata": {}, "outputs": [], "source": ["# package import statement\n", "from SmartApi import SmartConnect #or from smartapi.smartConnect import SmartConnect"]}, {"cell_type": "code", "execution_count": null, "id": "80b44f2d", "metadata": {}, "outputs": [], "source": ["api_key = \"\" # api key from https://smartapi.angelbroking.com/apps\n", "client_id = \"\" # from https://smartapi.angelbroking.com/profile\n", "pwd = \"\" # 4 digit PIN\n", "totp = \"\" # Generate TOTP using any authenticator as this is temporary, you need to scan the QR using https://smartapi.angelbroking.com/enable-totp"]}, {"cell_type": "code", "execution_count": 3, "id": "bf29082e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[I 250720 19:32:24 smartConnect:124] in pool\n"]}], "source": ["#create object of call\n", "obj=SmartConnect(api_key)"]}, {"cell_type": "code", "execution_count": 4, "id": "4919c50c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'status': True, 'message': 'SUCCESS', 'errorcode': '', 'data': {'clientcode': 'S60593162', 'name': 'SUBHADA S', 'email': '', 'mobileno': '', 'exchanges': ['nse_fo', 'nse_cm', 'cde_fo', 'ncx_fo', 'bse_fo', 'bse_cm', 'mcx_fo'], 'products': ['MARGIN', 'MIS', 'NRML', 'CNC', 'CO', 'BO'], 'lastlogintime': '', 'broker': '', 'jwtToken': 'Bearer eyJhbGciOiJIUzUxMiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.f7sI_wuJlwm8W3GoDaSBbpng9REFfOqfu88dyUdp2eRKKmrad5E2EJkFeQ1AkavcMr9ZImDt2QEn8G1Rq-Ty2g', 'refreshToken': 'eyJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.N4_u13yt6V7EAb2AcbEsJLaZckW1N-Hu6cTg6bYCmPrktocclD30kA8VkDVyIFfEcshsTNwAbVz-VA32lLMYWw', 'feedToken': 'eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6IlM2MDU5MzE2MiIsImlhdCI6MTc1MzAyMDE0NSwiZXhwIjoxNzUzMTA2NTQ1fQ.Ynhq9-wG9Z2ewsirJTGGAnzfzUJv3Fd2tWl8ra1pPqPuBUI5hIoGL4OCcE9Ji3VxQGCVbm4OrWev_bwAggrknA'}}\n"]}], "source": ["data = obj.generateSession(client_id,pwd,totp)\n", "# data = obj.generateSession(\"Your Client ID\",\"Your Password\",\"Your totp\")\n", "print(data)"]}, {"cell_type": "code", "execution_count": 5, "id": "9c9a411b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["token:  eyJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.N4_u13yt6V7EAb2AcbEsJLaZckW1N-Hu6cTg6bYCmPrktocclD30kA8VkDVyIFfEcshsTNwAbVz-VA32lLMYWw\n"]}], "source": ["refreshToken= data['data']['refreshToken']\n", "print(\"token: \", refreshToken)"]}, {"cell_type": "code", "execution_count": 6, "id": "9b1795c3", "metadata": {}, "outputs": [], "source": ["feedToken=obj.getfeedToken()\n", "userProfile= obj.getProfile(refreshToken)"]}, {"cell_type": "code", "execution_count": 7, "id": "8d15c4d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'status': True, 'message': 'SUCCESS', 'errorcode': '', 'data': [['2025-01-07T00:00:00+05:30', 779.85, 783.85, 774.85, 778.75, 9669152], ['2025-01-08T00:00:00+05:30', 780.9, 783.95, 760.1, 771.15, 15641490], ['2025-01-09T00:00:00+05:30', 771.15, 771.35, 726.25, 760.45, 15351029], ['2025-01-10T00:00:00+05:30', 760.35, 763.4, 742.05, 743.25, 11779296], ['2025-01-13T00:00:00+05:30', 741.5, 743.95, 722.5, 729.5, 10414733], ['2025-01-14T00:00:00+05:30', 732.95, 757.0, 732.95, 748.15, 11960975], ['2025-01-15T00:00:00+05:30', 753.15, 758.9, 750.0, 753.7, 7792299], ['2025-01-16T00:00:00+05:30', 760.4, 776.7, 760.4, 766.3, 11816079], ['2025-01-17T00:00:00+05:30', 766.3, 767.95, 759.4, 764.1, 5766573], ['2025-01-20T00:00:00+05:30', 769.0, 785.0, 767.85, 779.25, 9198929], ['2025-01-21T00:00:00+05:30', 784.8, 784.9, 756.05, 759.05, 13751780], ['2025-01-22T00:00:00+05:30', 762.0, 763.0, 738.0, 753.45, 9908339], ['2025-01-23T00:00:00+05:30', 750.0, 755.65, 743.1, 745.9, 10986405], ['2025-01-24T00:00:00+05:30', 749.9, 753.7, 739.0, 744.15, 10579213], ['2025-01-27T00:00:00+05:30', 740.0, 755.35, 735.9, 749.2, 11433353], ['2025-01-28T00:00:00+05:30', 757.0, 759.75, 745.4, 752.4, 14140860], ['2025-01-29T00:00:00+05:30', 753.0, 760.8, 749.8, 758.45, 9251973], ['2025-01-30T00:00:00+05:30', 758.55, 765.0, 754.75, 762.6, 11085824], ['2025-01-31T00:00:00+05:30', 764.9, 777.55, 759.5, 772.9, 10083763], ['2025-02-01T00:00:00+05:30', 774.8, 778.75, 752.15, 766.0, 9363077], ['2025-02-03T00:00:00+05:30', 762.8, 762.8, 753.15, 760.95, 7862190], ['2025-02-04T00:00:00+05:30', 767.0, 781.25, 765.65, 779.2, 12020807], ['2025-02-05T00:00:00+05:30', 780.0, 782.0, 765.0, 766.05, 13419162], ['2025-02-06T00:00:00+05:30', 769.0, 770.85, 750.05, 752.25, 32745391]]}\n"]}], "source": ["#Historic api\n", "try:\n", "    historicParam={\n", "    \"exchange\": \"NSE\",\n", "    \"symboltoken\": \"3045\",\n", "    \"interval\": \"ONE_DAY\",\n", "    \"fromdate\": \"2025-01-06 09:00\", \n", "    \"todate\": \"2025-02-06 09:16\"\n", "    }\n", "    cdata = obj.getCandleData(historicParam)\n", "    print(cdata)\n", "\n", "except Exception as e:\n", "    print(\"Historic Api failed: {}\".format(e.message))\n", "#logout\n", "# try:\n", "#     logout=obj.terminateSession('client_id')\n", "#     # logout=obj.terminateSession('Your Client Id')\n", "#     print(\"Logout Successfull\")\n", "# except Exception as e:\n", "#     print(\"<PERSON>gout failed: {}\".format(e.message))"]}, {"cell_type": "code", "execution_count": 8, "id": "801193ba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'exchange': 'NSE', 'symboltoken': '99926000', 'interval': 'ONE_DAY', 'fromdate': '2024-07-20 19:32', 'todate': '2025-07-20 19:32'}\n", "{'status': True, 'message': 'SUCCESS', 'errorcode': '', 'data': [['2024-07-22T00:00:00+05:30', 24445.75, 24595.2, 24362.3, 24509.25, 0], ['2024-07-23T00:00:00+05:30', 24568.9, 24582.55, 24074.2, 24479.05, 0], ['2024-07-24T00:00:00+05:30', 24444.95, 24504.25, 24307.25, 24413.5, 0], ['2024-07-25T00:00:00+05:30', 24230.95, 24426.15, 24210.8, 24406.1, 0], ['2024-07-26T00:00:00+05:30', 24423.35, 24861.15, 24410.9, 24834.85, 0], ['2024-07-29T00:00:00+05:30', 24943.3, 24999.75, 24774.6, 24836.1, 0], ['2024-07-30T00:00:00+05:30', 24839.4, 24971.75, 24798.65, 24857.3, 0], ['2024-07-31T00:00:00+05:30', 24886.7, 24984.6, 24856.5, 24951.15, 0], ['2024-08-01T00:00:00+05:30', 25030.95, 25078.3, 24956.4, 25010.9, 0], ['2024-08-02T00:00:00+05:30', 24789.0, 24851.9, 24686.85, 24717.7, 0], ['2024-08-05T00:00:00+05:30', 24302.85, 24350.05, 23893.7, 24055.6, 0], ['2024-08-06T00:00:00+05:30', 24189.85, 24382.6, 23960.4, 23992.55, 0], ['2024-08-07T00:00:00+05:30', 24289.4, 24337.7, 24184.9, 24297.5, 0], ['2024-08-08T00:00:00+05:30', 24248.55, 24340.5, 24079.7, 24117.0, 0], ['2024-08-09T00:00:00+05:30', 24386.85, 24419.75, 24311.2, 24367.5, 0], ['2024-08-12T00:00:00+05:30', 24320.05, 24472.8, 24212.1, 24347.0, 0], ['2024-08-13T00:00:00+05:30', 24342.35, 24359.95, 24116.5, 24139.0, 0], ['2024-08-14T00:00:00+05:30', 24184.4, 24196.5, 24099.7, 24143.75, 0], ['2024-08-16T00:00:00+05:30', 24334.85, 24563.9, 24204.5, 24541.15, 0], ['2024-08-19T00:00:00+05:30', 24636.35, 24638.8, 24522.95, 24572.65, 0], ['2024-08-20T00:00:00+05:30', 24648.9, 24734.3, 24607.2, 24698.85, 0], ['2024-08-21T00:00:00+05:30', 24680.55, 24787.95, 24654.5, 24770.2, 0], ['2024-08-22T00:00:00+05:30', 24863.4, 24867.35, 24784.45, 24811.5, 0], ['2024-08-23T00:00:00+05:30', 24845.4, 24858.4, 24771.65, 24823.15, 0], ['2024-08-26T00:00:00+05:30', 24906.1, 25043.8, 24874.7, 25010.6, 0], ['2024-08-27T00:00:00+05:30', 25024.8, 25073.1, 24973.65, 25017.75, 0], ['2024-08-28T00:00:00+05:30', 25030.8, 25129.6, 24964.65, 25052.35, 0], ['2024-08-29T00:00:00+05:30', 25035.3, 25192.9, 24998.5, 25151.95, 0], ['2024-08-30T00:00:00+05:30', 25249.7, 25268.35, 25199.4, 25235.9, 0], ['2024-09-02T00:00:00+05:30', 25333.6, 25333.65, 25235.5, 25278.7, 0], ['2024-09-03T00:00:00+05:30', 25313.4, 25321.7, 25235.8, 25279.85, 0], ['2024-09-04T00:00:00+05:30', 25089.95, 25216.0, 25083.8, 25198.7, 0], ['2024-09-05T00:00:00+05:30', 25250.5, 25275.45, 25127.75, 25145.1, 0], ['2024-09-06T00:00:00+05:30', 25093.7, 25168.75, 24801.3, 24852.15, 0], ['2024-09-09T00:00:00+05:30', 24823.4, 24957.5, 24753.15, 24936.4, 0], ['2024-09-10T00:00:00+05:30', 24999.4, 25130.5, 24896.8, 25041.1, 0], ['2024-09-11T00:00:00+05:30', 25034.0, 25113.7, 24885.15, 24918.45, 0], ['2024-09-12T00:00:00+05:30', 25059.65, 25433.35, 24941.45, 25388.9, 0], ['2024-09-13T00:00:00+05:30', 25430.45, 25430.5, 25292.45, 25356.5, 0], ['2024-09-16T00:00:00+05:30', 25406.65, 25445.7, 25336.2, 25383.75, 0], ['2024-09-17T00:00:00+05:30', 25416.9, 25441.65, 25352.25, 25418.55, 0], ['2024-09-18T00:00:00+05:30', 25402.4, 25482.2, 25285.55, 25377.55, 0], ['2024-09-19T00:00:00+05:30', 25487.05, 25611.95, 25376.05, 25415.8, 0], ['2024-09-20T00:00:00+05:30', 25525.95, 25849.25, 25426.6, 25790.95, 0], ['2024-09-23T00:00:00+05:30', 25872.55, 25956.0, 25847.35, 25939.05, 0], ['2024-09-24T00:00:00+05:30', 25921.45, 26011.55, 25886.85, 25940.4, 0], ['2024-09-25T00:00:00+05:30', 25899.45, 26032.8, 25871.35, 26004.15, 0], ['2024-09-26T00:00:00+05:30', 26005.4, 26250.9, 25998.4, 26216.05, 0], ['2024-09-27T00:00:00+05:30', 26248.25, 26277.35, 26151.4, 26178.95, 0], ['2024-09-30T00:00:00+05:30', 26061.3, 26134.7, 25794.1, 25810.85, 0], ['2024-10-01T00:00:00+05:30', 25788.45, 25907.6, 25739.2, 25796.9, 0], ['2024-10-03T00:00:00+05:30', 25452.85, 25639.45, 25230.3, 25250.1, 0], ['2024-10-04T00:00:00+05:30', 25181.9, 25485.05, 24966.8, 25014.6, 0], ['2024-10-07T00:00:00+05:30', 25084.1, 25143.0, 24694.35, 24795.75, 0], ['2024-10-08T00:00:00+05:30', 24832.2, 25044.0, 24756.8, 25013.15, 0], ['2024-10-09T00:00:00+05:30', 25065.8, 25234.05, 24947.7, 24981.95, 0], ['2024-10-10T00:00:00+05:30', 25067.05, 25134.05, 24979.4, 24998.45, 0], ['2024-10-11T00:00:00+05:30', 24985.3, 25028.65, 24920.05, 24964.25, 0], ['2024-10-14T00:00:00+05:30', 25023.45, 25159.75, 25017.5, 25127.95, 0], ['2024-10-15T00:00:00+05:30', 25186.3, 25212.05, 25008.15, 25057.35, 0], ['2024-10-16T00:00:00+05:30', 25008.55, 25093.4, 24908.45, 24971.3, 0], ['2024-10-17T00:00:00+05:30', 25027.4, 25029.5, 24728.9, 24749.85, 0], ['2024-10-18T00:00:00+05:30', 24664.95, 24886.2, 24567.65, 24854.05, 0], ['2024-10-21T00:00:00+05:30', 24956.15, 24978.3, 24679.6, 24781.1, 0], ['2024-10-22T00:00:00+05:30', 24798.65, 24882.0, 24445.8, 24472.1, 0], ['2024-10-23T00:00:00+05:30', 24378.15, 24604.25, 24378.1, 24435.5, 0], ['2024-10-24T00:00:00+05:30', 24412.7, 24480.65, 24341.2, 24399.4, 0], ['2024-10-25T00:00:00+05:30', 24418.05, 24440.25, 24073.9, 24180.8, 0], ['2024-10-28T00:00:00+05:30', 24251.1, 24492.6, 24134.9, 24339.15, 0], ['2024-10-29T00:00:00+05:30', 24328.85, 24484.5, 24140.85, 24466.85, 0], ['2024-10-30T00:00:00+05:30', 24371.45, 24498.2, 24307.3, 24340.85, 0], ['2024-10-31T00:00:00+05:30', 24349.85, 24372.45, 24172.6, 24205.35, 0], ['2024-11-01T00:00:00+05:30', 24302.75, 24368.25, 24280.2, 24304.35, 0], ['2024-11-04T00:00:00+05:30', 24315.75, 24316.75, 23816.15, 23995.35, 0], ['2024-11-05T00:00:00+05:30', 23916.5, 24229.05, 23842.75, 24213.3, 0], ['2024-11-06T00:00:00+05:30', 24308.75, 24537.6, 24204.05, 24484.05, 0], ['2024-11-07T00:00:00+05:30', 24489.6, 24503.35, 24179.05, 24199.35, 0], ['2024-11-08T00:00:00+05:30', 24207.7, 24276.15, 24066.65, 24148.2, 0], ['2024-11-11T00:00:00+05:30', 24087.25, 24336.8, 24004.6, 24141.3, 0], ['2024-11-12T00:00:00+05:30', 24225.8, 24242.0, 23839.15, 23883.45, 0], ['2024-11-13T00:00:00+05:30', 23822.45, 23873.6, 23509.6, 23559.05, 0], ['2024-11-14T00:00:00+05:30', 23542.15, 23675.9, 23484.15, 23532.7, 0], ['2024-11-18T00:00:00+05:30', 23605.3, 23606.8, 23350.4, 23453.8, 0], ['2024-11-19T00:00:00+05:30', 23529.55, 23780.65, 23464.8, 23518.5, 0], ['2024-11-21T00:00:00+05:30', 23488.45, 23507.3, 23263.15, 23349.9, 0], ['2024-11-22T00:00:00+05:30', 23411.8, 23956.1, 23359.0, 23907.25, 0], ['2024-11-25T00:00:00+05:30', 24253.55, 24351.55, 24135.45, 24221.9, 0], ['2024-11-26T00:00:00+05:30', 24343.3, 24343.3, 24125.4, 24194.5, 0], ['2024-11-27T00:00:00+05:30', 24204.8, 24354.55, 24145.65, 24274.9, 0], ['2024-11-28T00:00:00+05:30', 24274.15, 24345.75, 23873.35, 23914.15, 0], ['2024-11-29T00:00:00+05:30', 23927.15, 24188.45, 23927.15, 24131.1, 0], ['2024-12-02T00:00:00+05:30', 24140.85, 24301.7, 24008.65, 24276.05, 0], ['2024-12-03T00:00:00+05:30', 24367.5, 24481.35, 24280.0, 24457.15, 0], ['2024-12-04T00:00:00+05:30', 24488.75, 24573.2, 24366.3, 24467.45, 0], ['2024-12-05T00:00:00+05:30', 24539.15, 24857.75, 24295.55, 24708.4, 0], ['2024-12-06T00:00:00+05:30', 24729.45, 24751.05, 24620.5, 24677.8, 0], ['2024-12-09T00:00:00+05:30', 24633.9, 24705.0, 24580.05, 24619.0, 0], ['2024-12-10T00:00:00+05:30', 24652.65, 24677.8, 24510.65, 24610.05, 0], ['2024-12-11T00:00:00+05:30', 24620.5, 24691.75, 24583.85, 24641.8, 0], ['2024-12-12T00:00:00+05:30', 24604.45, 24675.25, 24527.95, 24548.7, 0], ['2024-12-13T00:00:00+05:30', 24498.35, 24792.3, 24180.8, 24768.3, 0], ['2024-12-16T00:00:00+05:30', 24753.4, 24781.25, 24601.75, 24668.25, 0], ['2024-12-17T00:00:00+05:30', 24584.8, 24624.1, 24303.45, 24336.0, 0], ['2024-12-18T00:00:00+05:30', 24297.95, 24394.45, 24149.85, 24198.85, 0], ['2024-12-19T00:00:00+05:30', 23877.15, 24004.9, 23870.3, 23951.7, 0], ['2024-12-20T00:00:00+05:30', 23960.7, 24065.8, 23537.35, 23587.5, 0], ['2024-12-23T00:00:00+05:30', 23738.2, 23869.55, 23647.2, 23753.45, 0], ['2024-12-24T00:00:00+05:30', 23769.1, 23867.65, 23685.15, 23727.65, 0], ['2024-12-26T00:00:00+05:30', 23775.8, 23854.5, 23653.6, 23750.2, 0], ['2024-12-27T00:00:00+05:30', 23801.4, 23938.85, 23800.6, 23813.4, 0], ['2024-12-30T00:00:00+05:30', 23796.9, 23915.35, 23599.3, 23644.9, 0], ['2024-12-31T00:00:00+05:30', 23560.6, 23689.85, 23460.45, 23644.8, 0], ['2025-01-01T00:00:00+05:30', 23637.65, 23822.8, 23562.8, 23742.9, 0], ['2025-01-02T00:00:00+05:30', 23783.0, 24226.7, 23751.55, 24188.65, 0], ['2025-01-03T00:00:00+05:30', 24196.4, 24196.45, 23976.0, 24004.75, 0], ['2025-01-06T00:00:00+05:30', 24045.8, 24089.95, 23551.9, 23616.05, 0], ['2025-01-07T00:00:00+05:30', 23679.9, 23795.2, 23637.8, 23707.9, 0], ['2025-01-08T00:00:00+05:30', 23746.65, 23751.85, 23496.15, 23688.95, 0], ['2025-01-09T00:00:00+05:30', 23674.75, 23689.5, 23503.05, 23526.5, 0], ['2025-01-10T00:00:00+05:30', 23551.9, 23596.6, 23344.35, 23431.5, 0], ['2025-01-13T00:00:00+05:30', 23195.4, 23340.95, 23047.25, 23085.95, 0], ['2025-01-14T00:00:00+05:30', 23165.9, 23264.95, 23134.15, 23176.05, 0], ['2025-01-15T00:00:00+05:30', 23250.45, 23293.65, 23146.45, 23213.2, 0], ['2025-01-16T00:00:00+05:30', 23377.25, 23391.65, 23272.05, 23311.8, 0], ['2025-01-17T00:00:00+05:30', 23277.1, 23292.1, 23100.35, 23203.2, 0], ['2025-01-20T00:00:00+05:30', 23290.4, 23391.1, 23170.65, 23344.75, 0], ['2025-01-21T00:00:00+05:30', 23421.65, 23426.3, 22976.85, 23024.65, 0], ['2025-01-22T00:00:00+05:30', 23099.15, 23169.55, 22981.3, 23155.35, 0], ['2025-01-23T00:00:00+05:30', 23128.3, 23270.8, 23090.65, 23205.35, 0], ['2025-01-24T00:00:00+05:30', 23183.9, 23347.3, 23050.0, 23092.2, 0], ['2025-01-27T00:00:00+05:30', 22940.15, 23007.45, 22786.9, 22829.15, 0], ['2025-01-28T00:00:00+05:30', 22960.45, 23137.95, 22857.65, 22957.25, 0], ['2025-01-29T00:00:00+05:30', 23026.75, 23183.35, 22976.5, 23163.1, 0], ['2025-01-30T00:00:00+05:30', 23169.5, 23322.05, 23139.2, 23249.5, 0], ['2025-01-31T00:00:00+05:30', 23296.75, 23546.8, 23277.4, 23508.4, 0], ['2025-02-01T00:00:00+05:30', 23528.6, 23632.45, 23318.3, 23482.15, 0], ['2025-02-03T00:00:00+05:30', 23319.35, 23381.6, 23222.0, 23361.05, 0], ['2025-02-04T00:00:00+05:30', 23509.9, 23762.75, 23423.15, 23739.25, 0], ['2025-02-05T00:00:00+05:30', 23801.75, 23807.3, 23680.45, 23696.3, 0], ['2025-02-06T00:00:00+05:30', 23761.95, 23773.55, 23556.25, 23603.35, 0], ['2025-02-07T00:00:00+05:30', 23649.5, 23694.5, 23443.2, 23559.95, 0], ['2025-02-10T00:00:00+05:30', 23543.8, 23568.6, 23316.3, 23381.6, 0], ['2025-02-11T00:00:00+05:30', 23383.55, 23390.05, 22986.65, 23071.8, 0], ['2025-02-12T00:00:00+05:30', 23050.8, 23144.7, 22798.35, 23045.25, 0], ['2025-02-13T00:00:00+05:30', 23055.75, 23235.5, 22992.2, 23031.4, 0], ['2025-02-14T00:00:00+05:30', 23096.45, 23133.7, 22774.85, 22929.25, 0], ['2025-02-17T00:00:00+05:30', 22809.9, 22974.2, 22725.45, 22959.5, 0], ['2025-02-18T00:00:00+05:30', 22963.65, 22992.5, 22801.5, 22945.3, 0], ['2025-02-19T00:00:00+05:30', 22847.25, 23049.95, 22814.85, 22932.9, 0], ['2025-02-20T00:00:00+05:30', 22821.1, 22923.85, 22812.75, 22913.15, 0], ['2025-02-21T00:00:00+05:30', 22857.2, 22921.0, 22720.3, 22795.9, 0], ['2025-02-24T00:00:00+05:30', 22609.35, 22668.05, 22518.8, 22553.35, 0], ['2025-02-25T00:00:00+05:30', 22516.45, 22625.3, 22513.9, 22547.55, 0], ['2025-02-27T00:00:00+05:30', 22568.95, 22613.3, 22508.4, 22545.05, 0], ['2025-02-28T00:00:00+05:30', 22433.4, 22450.35, 22104.85, 22124.7, 0], ['2025-03-03T00:00:00+05:30', 22194.55, 22261.55, 22004.7, 22119.3, 0], ['2025-03-04T00:00:00+05:30', 21974.45, 22105.05, 21964.6, 22082.65, 0], ['2025-03-05T00:00:00+05:30', 22073.05, 22394.9, 22067.8, 22337.3, 0], ['2025-03-06T00:00:00+05:30', 22476.35, 22556.45, 22245.85, 22544.7, 0], ['2025-03-07T00:00:00+05:30', 22508.65, 22633.8, 22464.75, 22552.5, 0], ['2025-03-10T00:00:00+05:30', 22521.85, 22676.75, 22429.05, 22460.3, 0], ['2025-03-11T00:00:00+05:30', 22345.95, 22522.1, 22314.7, 22497.9, 0], ['2025-03-12T00:00:00+05:30', 22536.35, 22577.4, 22329.55, 22470.5, 0], ['2025-03-13T00:00:00+05:30', 22541.5, 22558.05, 22377.35, 22397.2, 0], ['2025-03-17T00:00:00+05:30', 22353.15, 22577.0, 22353.15, 22508.75, 0], ['2025-03-18T00:00:00+05:30', 22662.25, 22857.8, 22599.2, 22834.3, 0], ['2025-03-19T00:00:00+05:30', 22874.95, 22940.7, 22807.95, 22907.6, 0], ['2025-03-20T00:00:00+05:30', 23036.6, 23216.7, 22973.95, 23190.65, 0], ['2025-03-21T00:00:00+05:30', 23168.25, 23402.7, 23132.8, 23350.4, 0], ['2025-03-24T00:00:00+05:30', 23515.4, 23708.75, 23433.5, 23658.35, 0], ['2025-03-25T00:00:00+05:30', 23751.5, 23869.6, 23601.4, 23668.65, 0], ['2025-03-26T00:00:00+05:30', 23700.95, 23736.5, 23451.7, 23486.85, 0], ['2025-03-27T00:00:00+05:30', 23433.95, 23646.45, 23412.2, 23591.95, 0], ['2025-03-28T00:00:00+05:30', 23600.4, 23649.2, 23450.2, 23519.35, 0], ['2025-04-01T00:00:00+05:30', 23341.1, 23565.15, 23136.4, 23165.7, 0], ['2025-04-02T00:00:00+05:30', 23192.6, 23350.0, 23158.45, 23332.35, 0], ['2025-04-03T00:00:00+05:30', 23150.3, 23306.5, 23145.8, 23250.1, 0], ['2025-04-04T00:00:00+05:30', 23190.4, 23214.7, 22857.45, 22904.45, 0], ['2025-04-07T00:00:00+05:30', 21758.4, 22254.0, 21743.65, 22161.6, 0], ['2025-04-08T00:00:00+05:30', 22446.75, 22697.2, 22270.85, 22535.85, 0], ['2025-04-09T00:00:00+05:30', 22460.3, 22468.7, 22353.25, 22399.15, 0], ['2025-04-11T00:00:00+05:30', 22695.4, 22923.9, 22695.4, 22828.55, 0], ['2025-04-15T00:00:00+05:30', 23368.35, 23368.35, 23207.0, 23328.55, 0], ['2025-04-16T00:00:00+05:30', 23344.1, 23452.2, 23273.05, 23437.2, 0], ['2025-04-17T00:00:00+05:30', 23401.85, 23872.35, 23298.55, 23851.65, 0], ['2025-04-21T00:00:00+05:30', 23949.15, 24189.55, 23903.65, 24125.55, 0], ['2025-04-22T00:00:00+05:30', 24185.4, 24242.6, 24072.0, 24167.25, 0], ['2025-04-23T00:00:00+05:30', 24357.6, 24359.3, 24119.95, 24328.95, 0], ['2025-04-24T00:00:00+05:30', 24277.9, 24347.85, 24216.15, 24246.7, 0], ['2025-04-25T00:00:00+05:30', 24289.0, 24365.45, 23847.85, 24039.35, 0], ['2025-04-28T00:00:00+05:30', 24070.25, 24355.1, 24054.05, 24328.5, 0], ['2025-04-29T00:00:00+05:30', 24370.7, 24457.65, 24290.75, 24335.95, 0], ['2025-04-30T00:00:00+05:30', 24342.05, 24396.15, 24198.75, 24334.2, 0], ['2025-05-02T00:00:00+05:30', 24311.9, 24589.15, 24238.5, 24346.7, 0], ['2025-05-05T00:00:00+05:30', 24419.5, 24526.4, 24400.65, 24461.15, 0], ['2025-05-06T00:00:00+05:30', 24500.75, 24509.65, 24331.8, 24379.6, 0], ['2025-05-07T00:00:00+05:30', 24233.3, 24449.6, 24220.0, 24414.4, 0], ['2025-05-08T00:00:00+05:30', 24431.5, 24447.25, 24150.2, 24273.8, 0], ['2025-05-09T00:00:00+05:30', 23935.75, 24164.25, 23935.75, 24008.0, 0], ['2025-05-12T00:00:00+05:30', 24420.1, 24944.8, 24378.85, 24924.7, 0], ['2025-05-13T00:00:00+05:30', 24864.05, 24973.8, 24547.5, 24578.35, 0], ['2025-05-14T00:00:00+05:30', 24613.8, 24767.55, 24535.55, 24666.9, 0], ['2025-05-15T00:00:00+05:30', 24694.45, 25116.25, 24494.45, 25062.1, 0], ['2025-05-16T00:00:00+05:30', 25064.65, 25070.0, 24953.05, 25019.8, 0], ['2025-05-19T00:00:00+05:30', 25005.35, 25062.95, 24916.65, 24945.45, 0], ['2025-05-20T00:00:00+05:30', 24996.2, 25010.35, 24669.7, 24683.9, 0], ['2025-05-21T00:00:00+05:30', 24744.25, 24946.2, 24685.35, 24813.45, 0], ['2025-05-22T00:00:00+05:30', 24733.95, 24737.5, 24462.4, 24609.7, 0], ['2025-05-23T00:00:00+05:30', 24639.5, 24909.05, 24614.05, 24853.15, 0], ['2025-05-26T00:00:00+05:30', 24919.35, 25079.2, 24900.5, 25001.15, 0], ['2025-05-27T00:00:00+05:30', 24956.65, 25062.9, 24704.1, 24826.2, 0], ['2025-05-28T00:00:00+05:30', 24832.5, 24864.25, 24737.05, 24752.45, 0], ['2025-05-29T00:00:00+05:30', 24825.1, 24892.6, 24677.3, 24833.6, 0], ['2025-05-30T00:00:00+05:30', 24812.6, 24863.95, 24717.4, 24750.7, 0], ['2025-06-02T00:00:00+05:30', 24669.7, 24754.4, 24526.15, 24716.6, 0], ['2025-06-03T00:00:00+05:30', 24786.3, 24845.1, 24502.15, 24542.5, 0], ['2025-06-04T00:00:00+05:30', 24560.45, 24644.25, 24530.45, 24620.2, 0], ['2025-06-05T00:00:00+05:30', 24691.2, 24899.85, 24613.1, 24750.9, 0], ['2025-06-06T00:00:00+05:30', 24748.7, 25029.5, 24671.45, 25003.05, 0], ['2025-06-09T00:00:00+05:30', 25160.1, 25160.1, 25077.15, 25103.2, 0], ['2025-06-10T00:00:00+05:30', 25196.05, 25199.3, 25055.45, 25104.25, 0], ['2025-06-11T00:00:00+05:30', 25134.15, 25222.4, 25081.3, 25141.4, 0], ['2025-06-12T00:00:00+05:30', 25164.45, 25196.2, 24825.9, 24888.2, 0], ['2025-06-13T00:00:00+05:30', 24473.0, 24754.35, 24473.0, 24718.6, 0], ['2025-06-16T00:00:00+05:30', 24732.35, 24967.1, 24703.6, 24946.5, 0], ['2025-06-17T00:00:00+05:30', 24977.85, 24982.05, 24813.7, 24853.4, 0], ['2025-06-18T00:00:00+05:30', 24788.35, 24947.55, 24750.45, 24812.05, 0], ['2025-06-19T00:00:00+05:30', 24803.25, 24863.1, 24733.4, 24793.25, 0], ['2025-06-20T00:00:00+05:30', 24787.65, 25136.2, 24783.65, 25112.4, 0], ['2025-06-23T00:00:00+05:30', 24939.75, 25057.0, 24824.85, 24971.9, 0], ['2025-06-24T00:00:00+05:30', 25179.9, 25317.7, 24999.7, 25044.35, 0], ['2025-06-25T00:00:00+05:30', 25150.35, 25266.8, 25125.05, 25244.75, 0], ['2025-06-26T00:00:00+05:30', 25268.95, 25565.3, 25259.9, 25549.0, 0], ['2025-06-27T00:00:00+05:30', 25576.65, 25654.2, 25523.55, 25637.8, 0], ['2025-06-30T00:00:00+05:30', 25661.65, 25669.35, 25473.3, 25517.05, 0], ['2025-07-01T00:00:00+05:30', 25551.35, 25593.4, 25501.8, 25541.8, 0], ['2025-07-02T00:00:00+05:30', 25588.3, 25608.1, 25378.75, 25453.4, 0], ['2025-07-03T00:00:00+05:30', 25505.1, 25587.5, 25384.35, 25405.3, 0], ['2025-07-04T00:00:00+05:30', 25428.85, 25470.25, 25331.65, 25461.0, 0], ['2025-07-07T00:00:00+05:30', 25450.45, 25489.8, 25407.25, 25461.3, 0], ['2025-07-08T00:00:00+05:30', 25427.85, 25548.05, 25424.15, 25522.5, 0], ['2025-07-09T00:00:00+05:30', 25514.6, 25548.7, 25424.35, 25476.1, 0], ['2025-07-10T00:00:00+05:30', 25511.65, 25524.05, 25340.45, 25355.25, 0], ['2025-07-11T00:00:00+05:30', 25255.5, 25322.45, 25129.0, 25149.85, 0], ['2025-07-14T00:00:00+05:30', 25149.5, 25151.1, 25001.95, 25082.3, 0], ['2025-07-15T00:00:00+05:30', 25089.5, 25245.2, 25088.45, 25195.8, 0], ['2025-07-16T00:00:00+05:30', 25196.6, 25255.3, 25121.05, 25212.05, 0], ['2025-07-17T00:00:00+05:30', 25230.75, 25238.35, 25101.0, 25111.45, 0], ['2025-07-18T00:00:00+05:30', 25108.55, 25144.6, 24918.65, 24968.4, 0]]}\n", "Logout Successfull\n"]}], "source": ["# For Nifty50\n", "# link for symboltoken: https://smartapi.angelone.in/smartapi/forum/topic/3986/smartapi-now-provides-real-time-market-data-for-120-indices-across-nse-bse-and-mcx\n", "\n", "\n", "from datetime import datetime, timedelta\n", "\n", "# 1. Calculate the date range for the last 365 days\n", "todate_obj = datetime.now()\n", "fromdate_obj = todate_obj - <PERSON><PERSON><PERSON>(days=365)\n", "\n", "# 2. Format the dates into the required string format\n", "# The API expects the format \"YYYY-MM-DD HH:MM\"\n", "from_date_str = fromdate_obj.strftime(\"%Y-%m-%d %H:%M\")\n", "to_date_str = todate_obj.strftime(\"%Y-%m-%d %H:%M\")\n", "\n", "# 3. Construct the parameter dictionary with updated values\n", "# The symboltoken for the NIFTY 50 index is \"99926000\"\n", "try: \n", "    historicParam = {\n", "    \"exchange\": \"NSE\",\n", "    \"symboltoken\": \"99926000\", # NIFTY50\n", "    \"interval\": \"ONE_DAY\",\n", "    \"fromdate\": from_date_str,\n", "    \"todate\": to_date_str\n", "    }\n", "\n", "    # You can now use this 'historicParam' dictionary in your API call\n", "    print(historicParam)\n", "\n", "    cdata = obj.getCandleData(historicParam)\n", "    print(cdata)\n", "\n", "except Exception as e:\n", "    print(\"Historic Api failed: {}\".format(e.message))\n", "#logout\n", "try:\n", "    logout=obj.terminateSession(client_id)\n", "    # logout=obj.terminateSession('Your Client Id')\n", "    print(\"Logout Successfull\")\n", "except Exception as e:\n", "    print(\"Logout failed: {}\".format(e.message))"]}], "metadata": {"kernelspec": {"display_name": "smartapi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}